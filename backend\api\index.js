const express = require('express');
const cors = require('cors');
const serverless = require('serverless-http');
require('dotenv').config();

// Try to import with error handling
let testConnection, authRoutes, userRoutes, companyRoutes, projectRoutes, taskRoutes, attendanceRoutes, leaveRoutes, evaluationRoutes;

try {
  // Import database connection (try local first, then src)
  try {
    const database = require('./database');
    testConnection = database.testConnection;
  } catch (dbError) {
    const database = require('../src/config/database');
    testConnection = database.testConnection;
  }

  // Import routes
  authRoutes = require('../src/routes/auth.routes');
  userRoutes = require('../src/routes/user.routes');
  companyRoutes = require('../src/routes/company.routes');
  projectRoutes = require('../src/routes/project.routes');
  taskRoutes = require('../src/routes/task.routes');
  attendanceRoutes = require('../src/routes/attendance.routes');
  leaveRoutes = require('../src/routes/leave.routes');
  evaluationRoutes = require('../src/routes/evaluation.routes');
} catch (error) {
  console.error('Import error:', error);
  // Create fallback routes
  const router = express.Router();
  router.all('*', (req, res) => {
    res.status(500).json({
      success: false,
      message: 'Server configuration error',
      error: error.message
    });
  });

  authRoutes = userRoutes = companyRoutes = projectRoutes = taskRoutes = attendanceRoutes = leaveRoutes = evaluationRoutes = router;
}

const app = express();

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || '*',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialize database connection (but don't sync in serverless)
let dbInitialized = false;
const initializeDatabase = async () => {
  if (!dbInitialized && testConnection) {
    try {
      await testConnection();
      console.log('Database connection established');
      dbInitialized = true;
    } catch (error) {
      console.error('Database connection failed:', error);
      throw error;
    }
  } else if (!testConnection) {
    console.warn('Database connection function not available');
    dbInitialized = false;
  }
};

// Middleware to ensure database is connected
app.use(async (req, res, next) => {
  try {
    await initializeDatabase();
    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection failed'
    });
  }
});

// Routes
app.use('/auth', authRoutes);
app.use('/users', userRoutes);
app.use('/companies', companyRoutes);
app.use('/projects', projectRoutes);
app.use('/tasks', taskRoutes);
app.use('/attendance', attendanceRoutes);
app.use('/leaves', leaveRoutes);
app.use('/evaluations', evaluationRoutes);

// Root route
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to TeamCheck API',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

// Health check route
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    database: dbInitialized ? 'connected' : 'disconnected',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal Server Error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

module.exports = serverless(app);
