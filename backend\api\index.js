const express = require('express');
const cors = require('cors');
const serverless = require('serverless-http');
require('dotenv').config();

// Import database connection
const { sequelize, testConnection } = require('../src/config/database');

// Import routes
const authRoutes = require('../src/routes/auth.routes');
const userRoutes = require('../src/routes/user.routes');
const companyRoutes = require('../src/routes/company.routes');
const projectRoutes = require('../src/routes/project.routes');
const taskRoutes = require('../src/routes/task.routes');
const attendanceRoutes = require('../src/routes/attendance.routes');
const leaveRoutes = require('../src/routes/leave.routes');
const evaluationRoutes = require('../src/routes/evaluation.routes');

const app = express();

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || '*',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialize database connection (but don't sync in serverless)
let dbInitialized = false;
const initializeDatabase = async () => {
  if (!dbInitialized) {
    try {
      await testConnection();
      console.log('Database connection established');
      dbInitialized = true;
    } catch (error) {
      console.error('Database connection failed:', error);
      throw error;
    }
  }
};

// Middleware to ensure database is connected
app.use(async (req, res, next) => {
  try {
    await initializeDatabase();
    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection failed'
    });
  }
});

// Routes
app.use('/auth', authRoutes);
app.use('/users', userRoutes);
app.use('/companies', companyRoutes);
app.use('/projects', projectRoutes);
app.use('/tasks', taskRoutes);
app.use('/attendance', attendanceRoutes);
app.use('/leaves', leaveRoutes);
app.use('/evaluations', evaluationRoutes);

// Root route
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to TeamCheck API',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

// Health check route
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    database: dbInitialized ? 'connected' : 'disconnected',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal Server Error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

module.exports = serverless(app);
