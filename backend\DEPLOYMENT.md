# TeamCheck Backend Deployment Guide

## Deploying to Vercel

### Prerequisites

1. **Database Setup**: You need a MySQL database accessible from the internet. Options include:
   - **PlanetScale** (Recommended for serverless)
   - **Railway**
   - **AWS RDS**
   - **Google Cloud SQL**
   - **Azure Database for MySQL**

2. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)

### Step 1: Prepare Your Database

#### Option A: PlanetScale (Recommended)
1. Sign up at [planetscale.com](https://planetscale.com)
2. Create a new database
3. Get the connection string from the dashboard
4. Run the database initialization:
   ```bash
   node init-db.js
   ```

#### Option B: Other MySQL Providers
1. Create a MySQL database instance
2. Note down the connection details
3. Ensure the database is accessible from external connections
4. Run the database initialization script

### Step 2: Environment Variables

Set up the following environment variables in Vercel:

```
DB_HOST=your-database-host
DB_USER=your-database-username
DB_PASSWORD=your-database-password
DB_NAME=teamcheck
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h
NODE_ENV=production
FRONTEND_URL=https://your-frontend-domain.com
```

### Step 3: Deploy to Vercel

#### Method 1: Vercel CLI
1. Install Vercel CLI:
   ```bash
   npm i -g vercel
   ```

2. Login to Vercel:
   ```bash
   vercel login
   ```

3. Deploy:
   ```bash
   vercel --prod
   ```

#### Method 2: GitHub Integration
1. Push your code to GitHub
2. Connect your GitHub repository to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy automatically on push

### Step 4: Set Environment Variables in Vercel

1. Go to your project dashboard on Vercel
2. Navigate to Settings → Environment Variables
3. Add all the required environment variables
4. Redeploy the function

### Step 5: Test Your Deployment

1. Visit your Vercel URL
2. Test the health endpoint: `https://your-app.vercel.app/health`
3. Test authentication: `https://your-app.vercel.app/api/auth/register`

### Common Issues and Solutions

#### Issue 1: Database Connection Timeout
- **Solution**: Use connection pooling and ensure your database allows external connections
- **PlanetScale**: Use the connection string with SSL enabled

#### Issue 2: Function Timeout
- **Solution**: Optimize database queries and increase function timeout in vercel.json

#### Issue 3: CORS Issues
- **Solution**: Set the correct FRONTEND_URL environment variable

#### Issue 4: Environment Variables Not Loading
- **Solution**: Ensure all environment variables are set in Vercel dashboard and redeploy

### Database Initialization

Since Vercel functions are stateless, you need to initialize your database separately:

1. **Local Initialization**:
   ```bash
   # Set your production database credentials in .env
   node init-db.js
   ```

2. **Using a Migration Service**:
   - Consider using database migration tools for production
   - PlanetScale has built-in schema management

### Monitoring and Logs

1. **Vercel Dashboard**: View function logs and metrics
2. **Database Monitoring**: Use your database provider's monitoring tools
3. **Error Tracking**: Consider integrating Sentry or similar services

### Security Considerations

1. **Environment Variables**: Never commit .env files to version control
2. **Database Security**: Use strong passwords and enable SSL
3. **JWT Secret**: Use a strong, random JWT secret
4. **CORS**: Set specific frontend URLs instead of wildcards in production

### Performance Optimization

1. **Database Connections**: Use connection pooling
2. **Query Optimization**: Index frequently queried columns
3. **Caching**: Consider implementing Redis for session management
4. **Function Size**: Keep dependencies minimal

### Scaling Considerations

1. **Database**: Choose a database that can handle your expected load
2. **Function Limits**: Vercel has execution time and memory limits
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **CDN**: Use Vercel's CDN for static assets
